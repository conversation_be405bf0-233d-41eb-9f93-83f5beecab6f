import React from 'react'
import { Link } from 'react-router-dom'

function HeroBottomText() {
  return (
    <div>
      {/* <p className='text-white text-1xl'>
         &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;K72 est une agence qui pense <br /> chaque action pour nourrir la marque. Demain, <br /> dans 5 mois et dans 5 ans. On cherche la <br /> friction qui crée l’étincelle pour générer de <br /> l’émotion. Pour assurer une relation honnête, <br /> on est sans filtre, on <br /> dit ce qui doit être dit, on fait ce qui doit être fait.
      </p> */}

      <div className='font-[font2] text-center flex items-center gap-2 justify-center'>
        <div className='text-white hover:border-[#D3FD50] hover:text-[#D3FD50] leading-[6vw] overflow-hidden border-2 border-white rounded-full px-5 uppercase'>
        <Link className='text-[6vw]' to='/projects'>Projects</Link>
        </div>
        <div className='text-white hover:border-[#D3FD50] hover:text-[#D3FD50] leading-[6vw] overflow-hidden border-2 border-white rounded-full px-14 uppercase'>
        <Link className='text-[6vw]' to='/agence'>Agence</Link>
        </div>
      </div>
    </div>
  )
}

export default HeroBottomText
