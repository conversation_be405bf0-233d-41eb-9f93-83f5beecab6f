import React, { useRef } from 'react'
import { useGSAP } from '@gsap/react'
import gsap from 'gsap'
import { useLocation } from 'react-router-dom'

function Stairs(props) {
    //here we get current path of the page, according to path we animation the loader
    const currentPath = useLocation().pathname
    console.log(currentPath)

    const stairParentRef = useRef()
    const pageRef = useRef(null);

    useGSAP(function () {
        const tl = gsap.timeline();

        tl.to(stairParentRef.current, {
            display: 'block',
        })

        tl.from('.stair', {
            height: 0,
            duration: 0.4,
            stagger: {
                amount: -0.3
            }
        }),
        tl.to('.stair', {
                y: '100%',
                stagger: {
                    amount: -0.3
                }
            }),
        tl.to(stairParentRef.current, {
                display: 'none',
            })
        tl.to('.stair', {
            y: '0%',
        })

        gsap.from(pageRef.current,{
            opacity:0,
            delay:1.2,
            scale:1.2
        })
    }, [currentPath])


    console.log(props.children)

    return (
        <div>
            <div ref={stairParentRef} className='h-screen w-full fixed z-20'>
                <div className='h-screen w-full flex'>
                    <div className='stair w-1/5 h-full bg-black'></div>
                    <div className='stair w-1/5 h-full bg-black'></div>
                    <div className='stair w-1/5 h-full bg-black'></div>
                    <div className='stair w-1/5 h-full bg-black'></div>
                    <div className='stair w-1/5 h-full bg-black'></div>
                </div>
            </div>
            <div ref={pageRef}>
                {props.children}
            </div>
        </div>
    )
}

export default Stairs
