import { useGSAP } from '@gsap/react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/all';
import React, { useRef } from 'react'

function Agence() {
  gsap.registerPlugin(ScrollTrigger);
  const imageDivRef = useRef(null);
  const imageRef = useRef(null);

  const imageArray = [
    'https://k72.ca/uploads/teamMembers/Carl_480x640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/Olivier_480x640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/Lawrence_480x640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/HugoJoseph_480x640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/ChantalG_480x640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/MyleneS_480x640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/SophieA_480x640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/Claire_480x640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/Michele_480X640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/MEL_480X640-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/CAMILLE_480X640_2-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/MAXIME_480X640_2-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/MEGGIE_480X640_2-480x640.jpg',
    'https://k72.ca/uploads/teamMembers/joel_480X640_3-480x640.jpg',
  ]

  useGSAP(function(){
    gsap.to(imageDivRef.current,{
      scrollTrigger:{
        trigger:imageDivRef.current,
        markers: true,
        start:'top 28%',
        end: 'top -67%',
        scrub: true,
        pin: true,
        //onUpdate inbuilt method all to define function in scrollTrigger
        onUpdate:(elem) =>{
          // console.log(elem)
          // console.log(Math.floor(elem.progress * imageArray.length));
          let imageIndex;
          // element progress is 0 to 1
          if(elem.progress<1){
            imageIndex = (Math.floor(elem.progress * imageArray.length))
          } else {
            imageIndex = imageArray.length -1;
          }
          imageRef.current.src = imageArray[imageIndex]
        }
      }
    })    //current value is below complete imageDiv
  })
  
  return (
   <div className='text-white'>
    <div className='section1'>
    <div ref={imageDivRef} className='w-60 h-80 overflow-hidden absolute top-58 rounded-4xl left-[30vw] bg-red-300'>
      <img ref={imageRef} className='w-full h-full object-cover' src="" alt="" />
    </div>
     <div className='font-[font2] relative'>
      <div className='mt-[55vh]'>
        <h1 className='text-[19vw] uppercase leading-[17vw] text-center'>
          Soixan7e <br />
          Douze
        </h1>
      </div>
      <div className='pl-[40%]'>
         <p className='text-4xl'>
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Notre curiosité nourrit notre créativité. On reste humbles et on dit non aux gros egos, même le vôtre. Une marque est vivante. Elle a des valeurs, une personnalité, une histoire. Si on oublie ça, on peut faire de bons chiffres à court terme, mais on la tue à long terme. C’est pour ça qu’on s’engage à donner de la perspective, pour bâtir des marques influentes.
        </p>
      </div>
    </div>
   </div>

   <div className="section2 w-full h-screen">
    
   </div>
   </div>
  )
}

export default Agence